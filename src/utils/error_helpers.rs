use crate::errors::AppError;

/// Centralized error handling utilities
pub struct ErrorHelper;

impl ErrorHelper {
    /// Create not found error with consistent format
    pub fn not_found(entity_type: &str, id: Option<&str>) -> AppError {
        match id {
            Some(id) => AppError::NotFound(format!("{} with id '{}' not found", entity_type, id)),
            None => AppError::NotFound(format!("{} not found", entity_type)),
        }
    }

    /// Create not found error for slug-based lookups
    pub fn not_found_by_slug(entity_type: &str, slug: &str) -> AppError {
        AppError::NotFound(format!("{} with slug '{}' not found", entity_type, slug))
    }

    /// Create conflict error with consistent format  
    pub fn conflict(entity_type: &str, field: &str, value: &str) -> AppError {
        AppError::Conflict(format!(
            "{} with {} '{}' already exists",
            entity_type, field, value
        ))
    }

    /// Create validation error with consistent format
    pub fn validation(message: &str) -> AppError {
        AppError::Validation(message.to_string())
    }

    /// Convert validation errors to AppError
    pub fn from_validation_errors(errors: validator::ValidationErrors) -> AppError {
        AppError::Validation(errors.to_string())
    }
}
