use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::category::models::{
        CategoryPaginationRequest, DieselCategory, NewCategory, PaginatedCategories,
        PaginatedCategoriesWithTracking, UpdateCategory, Category, CategoryWithTracking,
    },
    repository::base_repository::AsyncRepository,
    schema::categories,
    utils::pagination::PaginationMeta,
};
use async_trait::async_trait;
use diesel::prelude::*;
use std::sync::Arc;
use uuid::Uuid;

/// Parameters for creating a new category (reduces function argument count)
#[derive(Debug, Clone)]
pub struct CreateCategoryParams {
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: String,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
}

/// Category Repository Trait (DIP: Depend on abstractions)
#[async_trait]
pub trait CategoryRepositoryTrait: Send + Sync {
    // CRUD Operations
    async fn create(&self, params: CreateCategoryParams) -> Result<CategoryWithTracking>;
    async fn get_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking>;
    async fn get_by_slug_and_type(&self, slug: &str, category_type: &str) -> Result<CategoryWithTracking>;
    async fn update(&self, id: &Uuid, update_data: UpdateCategory) -> Result<CategoryWithTracking>;
    async fn delete(&self, id: &Uuid) -> Result<()>;

    // Query Operations
    async fn get_all(&self) -> Result<Vec<Category>>;
    async fn get_all_with_tracking(&self) -> Result<Vec<CategoryWithTracking>>;
    async fn get_by_type(&self, category_type: &str) -> Result<Vec<Category>>;
    async fn get_by_type_with_tracking(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>>;
    async fn get_with_pagination(&self, request: CategoryPaginationRequest) -> Result<PaginatedCategories>;
    async fn get_with_pagination_and_tracking(&self, request: CategoryPaginationRequest) -> Result<PaginatedCategoriesWithTracking>;

    // Validation Operations
    async fn exists_by_name_and_type(&self, name: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
    async fn exists_by_slug_and_type(&self, slug: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
}

/// Diesel implementation of CategoryRepositoryTrait
#[derive(Clone)]
pub struct DieselCategoryRepository {
    base: AsyncRepository,
}

impl DieselCategoryRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl CategoryRepositoryTrait for DieselCategoryRepository {
    async fn create(&self, params: CreateCategoryParams) -> Result<CategoryWithTracking> {
        let new_category = NewCategory {
            id: Uuid::new_v4(),
            name: params.name,
            slug: params.slug,
            description: params.description,
            category_type: params.category_type,
            is_active: params.is_active,
            created_by: params.created_by,
        };

        self.base
            .execute(move |conn| {
                let diesel_category: DieselCategory = diesel::insert_into(categories::table)
                    .values(&new_category)
                    .returning(DieselCategory::as_returning())
                    .get_result(conn)?;

                Ok(CategoryWithTracking::from(diesel_category))
            })
            .await
    }

    async fn get_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking> {
        let id = *id;
        self.base
            .execute_readonly(move |conn| {
                let diesel_category = categories::table
                    .filter(categories::id.eq(id))
                    .select(DieselCategory::as_select())
                    .first(conn)
                    .map_err(|e| match e {
                        diesel::result::Error::NotFound => AppError::NotFound("Category not found".into()),
                        _ => AppError::Internal(e.into()),
                    })?;

                Ok(CategoryWithTracking::from(diesel_category))
            })
            .await
    }

    async fn get_by_slug_and_type(&self, slug: &str, category_type: &str) -> Result<CategoryWithTracking> {
        let slug = slug.to_string();
        let category_type = category_type.to_string();
        
        self.base
            .execute_readonly(move |conn| {
                let diesel_category = categories::table
                    .filter(categories::slug.eq(&slug))
                    .filter(categories::category_type.eq(&category_type))
                    .select(DieselCategory::as_select())
                    .first(conn)
                    .map_err(|e| match e {
                        diesel::result::Error::NotFound => AppError::NotFound("Category not found".into()),
                        _ => AppError::Internal(e.into()),
                    })?;

                Ok(CategoryWithTracking::from(diesel_category))
            })
            .await
    }

    async fn update(&self, id: &Uuid, update_data: UpdateCategory) -> Result<CategoryWithTracking> {
        let id = *id;
        self.base
            .execute(move |conn| {
                let diesel_category: DieselCategory = diesel::update(categories::table)
                    .filter(categories::id.eq(id))
                    .set(&update_data)
                    .returning(DieselCategory::as_returning())
                    .get_result(conn)
                    .map_err(|e| match e {
                        diesel::result::Error::NotFound => AppError::NotFound("Category not found".into()),
                        _ => AppError::Internal(e.into()),
                    })?;

                Ok(CategoryWithTracking::from(diesel_category))
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        self.base
            .execute(move |conn| {
                let affected_rows = diesel::delete(categories::table)
                    .filter(categories::id.eq(id))
                    .execute(conn)?;

                if affected_rows == 0 {
                    return Err(AppError::NotFound("Category not found".into()));
                }

                Ok(())
            })
            .await
    }

    async fn get_all(&self) -> Result<Vec<Category>> {
        self.base
            .execute_readonly(move |conn| {
                let diesel_categories: Vec<DieselCategory> = categories::table
                    .select(DieselCategory::as_select())
                    .order(categories::name.asc())
                    .load(conn)?;

                Ok(diesel_categories.into_iter().map(Category::from).collect())
            })
            .await
    }

    async fn get_all_with_tracking(&self) -> Result<Vec<CategoryWithTracking>> {
        self.base
            .execute_readonly(move |conn| {
                let diesel_categories: Vec<DieselCategory> = categories::table
                    .select(DieselCategory::as_select())
                    .order(categories::name.asc())
                    .load(conn)?;

                Ok(diesel_categories.into_iter().map(CategoryWithTracking::from).collect())
            })
            .await
    }

    async fn get_by_type(&self, category_type: &str) -> Result<Vec<Category>> {
        let category_type = category_type.to_string();
        self.base
            .execute_readonly(move |conn| {
                let diesel_categories: Vec<DieselCategory> = categories::table
                    .filter(categories::category_type.eq(&category_type))
                    .select(DieselCategory::as_select())
                    .order(categories::name.asc())
                    .load(conn)?;

                Ok(diesel_categories.into_iter().map(Category::from).collect())
            })
            .await
    }

    async fn get_by_type_with_tracking(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>> {
        let category_type = category_type.to_string();
        self.base
            .execute_readonly(move |conn| {
                let diesel_categories: Vec<DieselCategory> = categories::table
                    .filter(categories::category_type.eq(&category_type))
                    .select(DieselCategory::as_select())
                    .order(categories::name.asc())
                    .load(conn)?;

                Ok(diesel_categories.into_iter().map(CategoryWithTracking::from).collect())
            })
            .await
    }

    async fn get_with_pagination(&self, request: CategoryPaginationRequest) -> Result<PaginatedCategories> {
        self.base
            .execute_readonly(move |conn| {
                let mut query = categories::table.into_boxed();

                // Apply filters
                if let Some(ref category_type) = request.category_type {
                    query = query.filter(categories::category_type.eq(category_type));
                }

                if let Some(is_active) = request.is_active {
                    query = query.filter(categories::is_active.eq(is_active));
                }

                if let Some(ref search) = request.search {
                    let search_pattern = format!("%{}%", search);
                    query = query.filter(
                        categories::name.ilike(search_pattern.clone())
                            .or(categories::description.ilike(search_pattern))
                    );
                }

                // Build count query
                let mut count_query = categories::table.into_boxed();
                if let Some(ref category_type) = request.category_type {
                    count_query = count_query.filter(categories::category_type.eq(category_type));
                }
                if let Some(is_active) = request.is_active {
                    count_query = count_query.filter(categories::is_active.eq(is_active));
                }
                if let Some(ref search) = request.search {
                    let search_pattern = format!("%{}%", search);
                    count_query = count_query.filter(
                        categories::name.ilike(search_pattern.clone())
                            .or(categories::description.ilike(search_pattern))
                    );
                }
                let total = count_query.count().get_result::<i64>(conn)?;

                // Apply pagination
                let offset = (request.page - 1) * request.limit;
                let diesel_categories: Vec<DieselCategory> = query
                    .select(DieselCategory::as_select())
                    .order(categories::name.asc())
                    .limit(request.limit as i64)
                    .offset(offset as i64)
                    .load(conn)?;

                let categories = diesel_categories.into_iter().map(Category::from).collect();
                let meta = PaginationMeta::new(request.page, request.limit, total);

                Ok(PaginatedCategories { categories, meta })
            })
            .await
    }

    async fn get_with_pagination_and_tracking(&self, request: CategoryPaginationRequest) -> Result<PaginatedCategoriesWithTracking> {
        self.base
            .execute_readonly(move |conn| {
                let mut query = categories::table.into_boxed();

                // Apply filters
                if let Some(ref category_type) = request.category_type {
                    query = query.filter(categories::category_type.eq(category_type));
                }

                if let Some(is_active) = request.is_active {
                    query = query.filter(categories::is_active.eq(is_active));
                }

                if let Some(ref search) = request.search {
                    let search_pattern = format!("%{}%", search);
                    query = query.filter(
                        categories::name.ilike(search_pattern.clone())
                            .or(categories::description.ilike(search_pattern))
                    );
                }

                // Build count query
                let mut count_query = categories::table.into_boxed();
                if let Some(ref category_type) = request.category_type {
                    count_query = count_query.filter(categories::category_type.eq(category_type));
                }
                if let Some(is_active) = request.is_active {
                    count_query = count_query.filter(categories::is_active.eq(is_active));
                }
                if let Some(ref search) = request.search {
                    let search_pattern = format!("%{}%", search);
                    count_query = count_query.filter(
                        categories::name.ilike(search_pattern.clone())
                            .or(categories::description.ilike(search_pattern))
                    );
                }
                let total = count_query.count().get_result::<i64>(conn)?;

                // Apply pagination
                let offset = (request.page - 1) * request.limit;
                let diesel_categories: Vec<DieselCategory> = query
                    .select(DieselCategory::as_select())
                    .order(categories::name.asc())
                    .limit(request.limit as i64)
                    .offset(offset as i64)
                    .load(conn)?;

                let categories = diesel_categories.into_iter().map(CategoryWithTracking::from).collect();
                let meta = PaginationMeta::new(request.page, request.limit, total);

                Ok(PaginatedCategoriesWithTracking { categories, meta })
            })
            .await
    }

    async fn exists_by_name_and_type(&self, name: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        let name = name.to_string();
        let category_type = category_type.to_string();
        let exclude_id = exclude_id.copied();

        self.base
            .execute_readonly(move |conn| {
                let mut query = categories::table
                    .filter(categories::name.eq(&name))
                    .filter(categories::category_type.eq(&category_type))
                    .into_boxed();

                if let Some(id) = exclude_id {
                    query = query.filter(categories::id.ne(id));
                }

                let count = query.count().get_result::<i64>(conn)?;
                Ok(count > 0)
            })
            .await
    }

    async fn exists_by_slug_and_type(&self, slug: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        let slug = slug.to_string();
        let category_type = category_type.to_string();
        let exclude_id = exclude_id.copied();

        self.base
            .execute_readonly(move |conn| {
                let mut query = categories::table
                    .filter(categories::slug.eq(&slug))
                    .filter(categories::category_type.eq(&category_type))
                    .into_boxed();

                if let Some(id) = exclude_id {
                    query = query.filter(categories::id.ne(id));
                }

                let count = query.count().get_result::<i64>(conn)?;
                Ok(count > 0)
            })
            .await
    }
}

// Type alias for dependency injection
pub type DynCategoryRepo = Arc<dyn CategoryRepositoryTrait>;
