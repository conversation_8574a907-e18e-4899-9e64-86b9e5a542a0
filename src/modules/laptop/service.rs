use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::{
        category::service_trait::CategoryServiceTrait,
        laptop::{
            models::{
                CreateLaptopRequest, CreatePriceRequest, CreateSpecificationRequest,
                LaptopPaginationRequest, NewLaptop, NewPrice, NewSpecification,
                PaginatedLaptopsDetailed, PaginatedLaptopsFull, PaginatedLaptopsPublic,
                Price, Specification, UpdateLaptopRequest, UpdatePriceRequest,
                UpdateSpecificationRequest, LaptopFullView, LaptopPublicView,
            },
            repository::{
                DieselLaptopRepository, DieselPriceRepository, DieselSpecificationRepository,
                DynLaptopRepo, DynPriceRepo, DynSpecificationRepo,
            },
            service_trait::{
                LaptopManagementServiceTrait, LaptopServiceTrait, PriceServiceTrait,
                SpecificationServiceTrait,
            },
        },
    },
    utils::{validation::helpers::validate_enhanced_and_execute, Error<PERSON><PERSON><PERSON>},
};
use async_trait::async_trait;
use chrono::Utc;
use std::sync::Arc;
use uuid::Uuid;

// ===== LAPTOP SERVICE =====

#[derive(Clone)]
pub struct LaptopService {
    repository: DynLaptopRepo,
    category_service: Arc<dyn CategoryServiceTrait>,
}

impl LaptopService {
    pub fn new(
        database: Database,
        category_service: Arc<dyn CategoryServiceTrait>,
    ) -> Self {
        Self {
            repository: Arc::new(DieselLaptopRepository::new(database)),
            category_service,
        }
    }
}

#[async_trait]
impl LaptopServiceTrait for LaptopService {
    async fn create_laptop(&self, request: CreateLaptopRequest, created_by: &Uuid) -> Result<LaptopFullView> {
        validate_enhanced_and_execute(request, |req| async move {
            // Validate category exists
            self.category_service.validate_category_exists(&req.category_id).await?;
            
            // Validate slug uniqueness
            self.validate_laptop_slug(&req.slug, None).await?;
            
            // Validate SKU uniqueness if provided
            if let Some(ref sku) = req.sku {
                self.validate_laptop_sku(sku, None).await?;
            }

            let new_laptop = NewLaptop {
                id: Uuid::new_v4(),
                category_id: req.category_id,
                brand: req.brand,
                model: req.model,
                full_name: req.full_name,
                slug: req.slug,
                sku: req.sku,
                market_region: req.market_region.unwrap_or_else(|| "Global".to_string()),
                release_date: req.release_date,
                description: req.description,
                image_urls: req.image_urls.map(|urls| urls.into_iter().map(Some).collect()),
                status: "draft".to_string(),
                is_featured: req.is_featured.unwrap_or(false),
                created_by: *created_by,
            };

            self.repository.create(new_laptop).await
        })
        .await
    }

    async fn get_laptop_by_id(&self, id: &Uuid) -> Result<LaptopFullView> {
        self.repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("Laptop", Some(&id.to_string())))
    }

    async fn get_laptop_by_slug(&self, slug: &str) -> Result<LaptopFullView> {
        self.repository
            .find_by_slug(slug)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("Laptop", Some(slug)))
    }

    async fn update_laptop(&self, id: &Uuid, request: UpdateLaptopRequest, _updated_by: &Uuid) -> Result<LaptopFullView> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if laptop exists
            let existing = self.get_laptop_by_id(id).await?;

            // Validate category if being updated
            if let Some(category_id) = req.category_id {
                self.category_service.validate_category_exists(&category_id).await?;
            }

            // Validate slug uniqueness if being updated
            if let Some(ref slug) = req.slug {
                self.validate_laptop_slug(slug, Some(id)).await?;
            }

            // Validate SKU uniqueness if being updated
            if let Some(ref sku) = req.sku {
                self.validate_laptop_sku(sku, Some(id)).await?;
            }

            let updated_laptop = NewLaptop {
                id: existing.id,
                category_id: req.category_id.unwrap_or(existing.category_id),
                brand: req.brand.unwrap_or(existing.brand),
                model: req.model.unwrap_or(existing.model),
                full_name: req.full_name.unwrap_or(existing.full_name),
                slug: req.slug.unwrap_or(existing.slug),
                sku: req.sku.or(existing.sku),
                market_region: req.market_region.unwrap_or(existing.market_region),
                release_date: req.release_date.or(existing.release_date),
                description: req.description.or(existing.description),
                image_urls: req.image_urls.map(|urls| urls.into_iter().map(Some).collect())
                    .or_else(|| Some(existing.image_urls.into_iter().map(Some).collect())),
                status: existing.status,
                is_featured: req.is_featured.unwrap_or(existing.is_featured),
                created_by: existing.created_by,
            };

            self.repository.update(id, updated_laptop).await
        })
        .await
    }

    async fn delete_laptop(&self, id: &Uuid) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;
        
        self.repository.delete(id).await
    }

    async fn get_public_laptops(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsPublic> {
        self.repository.get_public_with_pagination(request).await
    }

    async fn get_public_laptop_by_slug(&self, slug: &str) -> Result<LaptopPublicView> {
        let laptop = self.repository
            .find_by_slug(slug)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("Laptop", Some(slug)))?;

        // Only return if laptop is published
        if laptop.status != "published" {
            return Err(ErrorHelper::not_found("Laptop", Some(slug)));
        }

        Ok(LaptopPublicView {
            id: laptop.id,
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
        })
    }

    async fn increment_laptop_view_count(&self, id: &Uuid) -> Result<()> {
        self.repository.increment_view_count(id).await
    }

    async fn get_detailed_laptops(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsDetailed> {
        self.repository.get_detailed_with_pagination(request).await
    }

    async fn get_full_laptops(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsFull> {
        self.repository.get_full_with_pagination(request).await
    }

    async fn publish_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;
        
        self.repository.update_status(id, "published", updated_by).await
    }

    async fn archive_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;
        
        self.repository.update_status(id, "archived", updated_by).await
    }

    async fn set_laptop_featured(&self, id: &Uuid, is_featured: bool, updated_by: &Uuid) -> Result<()> {
        // Check if laptop exists
        self.get_laptop_by_id(id).await?;
        
        self.repository.update_featured(id, is_featured, updated_by).await
    }

    async fn validate_laptop_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        let exists = self.repository.exists_by_slug(slug, exclude_id).await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Laptop with slug '{}' already exists",
                slug
            )));
        }
        Ok(())
    }

    async fn validate_laptop_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        let exists = self.repository.exists_by_sku(sku, exclude_id).await?;
        if exists {
            return Err(AppError::Conflict(format!(
                "Laptop with SKU '{}' already exists",
                sku
            )));
        }
        Ok(())
    }
}

// ===== SPECIFICATION SERVICE =====

#[derive(Clone)]
pub struct SpecificationService {
    repository: DynSpecificationRepo,
    laptop_service: Arc<dyn LaptopServiceTrait>,
}

impl SpecificationService {
    pub fn new(
        database: Database,
        laptop_service: Arc<dyn LaptopServiceTrait>,
    ) -> Self {
        Self {
            repository: Arc::new(DieselSpecificationRepository::new(database)),
            laptop_service,
        }
    }
}

#[async_trait]
impl SpecificationServiceTrait for SpecificationService {
    async fn create_specification(&self, request: CreateSpecificationRequest) -> Result<Specification> {
        validate_enhanced_and_execute(request, |req| async move {
            // Validate laptop exists
            self.laptop_service.get_laptop_by_id(&req.laptop_id).await?;

            // Check if specification already exists for this laptop
            if let Some(_) = self.repository.find_by_laptop_id(&req.laptop_id).await? {
                return Err(AppError::Conflict(format!(
                    "Specification already exists for laptop {}",
                    req.laptop_id
                )));
            }

            let new_spec = NewSpecification {
                id: Uuid::new_v4(),
                laptop_id: req.laptop_id,
                cpu_brand: req.cpu_brand,
                cpu_model: req.cpu_model,
                ram_size: req.ram_size,
                ram_type: req.ram_type.map(|t| match t {
                    crate::modules::laptop::models::RamType::DDR3 => "DDR3".to_string(),
                    crate::modules::laptop::models::RamType::DDR4 => "DDR4".to_string(),
                    crate::modules::laptop::models::RamType::DDR5 => "DDR5".to_string(),
                    crate::modules::laptop::models::RamType::LPDDR4 => "LPDDR4".to_string(),
                    crate::modules::laptop::models::RamType::LPDDR5 => "LPDDR5".to_string(),
                }),
                ram_slots_total: req.ram_slots_total,
                ram_slots_available: req.ram_slots_available,
                ram_max_capacity: req.ram_max_capacity,
                ram_soldered: req.ram_soldered.unwrap_or(false),
                storage_type: match req.storage_type {
                    crate::modules::laptop::models::StorageType::SSD => "SSD".to_string(),
                    crate::modules::laptop::models::StorageType::HDD => "HDD".to_string(),
                    crate::modules::laptop::models::StorageType::Hybrid => "Hybrid".to_string(),
                },
                storage_capacity: req.storage_capacity,
                storage_slots_total: req.storage_slots_total,
                storage_slots_available: req.storage_slots_available,
                storage_max_capacity: req.storage_max_capacity,
                gpu_type: match req.gpu_type {
                    crate::modules::laptop::models::GpuType::Integrated => "Integrated".to_string(),
                    crate::modules::laptop::models::GpuType::Dedicated => "Dedicated".to_string(),
                },
                gpu_model: req.gpu_model,
                screen_size: req.screen_size,
                screen_resolution: match req.screen_resolution {
                    crate::modules::laptop::models::ScreenResolution::FHD => "FHD".to_string(),
                    crate::modules::laptop::models::ScreenResolution::TwoK => "2K".to_string(),
                    crate::modules::laptop::models::ScreenResolution::TwoPointFiveK => "2.5K".to_string(),
                    crate::modules::laptop::models::ScreenResolution::ThreeK => "3K".to_string(),
                    crate::modules::laptop::models::ScreenResolution::ThreePointFiveK => "3.5K".to_string(),
                    crate::modules::laptop::models::ScreenResolution::FourK => "4K".to_string(),
                },
                refresh_rate: req.refresh_rate,
                weight: req.weight,
                operating_system: req.operating_system,
            };

            self.repository.create(new_spec).await
        })
        .await
    }

    async fn get_specification_by_id(&self, id: &Uuid) -> Result<Specification> {
        self.repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("Specification", Some(&id.to_string())))
    }

    async fn get_specification_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Option<Specification>> {
        self.repository.find_by_laptop_id(laptop_id).await
    }

    async fn update_specification(&self, id: &Uuid, request: UpdateSpecificationRequest) -> Result<Specification> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if specification exists
            let existing = self.get_specification_by_id(id).await?;

            let updated_spec = NewSpecification {
                id: existing.id,
                laptop_id: existing.laptop_id,
                cpu_brand: req.cpu_brand.unwrap_or(existing.cpu_brand),
                cpu_model: req.cpu_model.unwrap_or(existing.cpu_model),
                ram_size: req.ram_size.unwrap_or(existing.ram_size),
                ram_type: req.ram_type.map(|t| match t {
                    crate::modules::laptop::models::RamType::DDR3 => "DDR3".to_string(),
                    crate::modules::laptop::models::RamType::DDR4 => "DDR4".to_string(),
                    crate::modules::laptop::models::RamType::DDR5 => "DDR5".to_string(),
                    crate::modules::laptop::models::RamType::LPDDR4 => "LPDDR4".to_string(),
                    crate::modules::laptop::models::RamType::LPDDR5 => "LPDDR5".to_string(),
                }).or(existing.ram_type),
                ram_slots_total: req.ram_slots_total.or(existing.ram_slots_total),
                ram_slots_available: req.ram_slots_available.or(existing.ram_slots_available),
                ram_max_capacity: req.ram_max_capacity.or(existing.ram_max_capacity),
                ram_soldered: req.ram_soldered.unwrap_or(existing.ram_soldered),
                storage_type: req.storage_type.map(|t| match t {
                    crate::modules::laptop::models::StorageType::SSD => "SSD".to_string(),
                    crate::modules::laptop::models::StorageType::HDD => "HDD".to_string(),
                    crate::modules::laptop::models::StorageType::Hybrid => "Hybrid".to_string(),
                }).unwrap_or(existing.storage_type),
                storage_capacity: req.storage_capacity.unwrap_or(existing.storage_capacity),
                storage_slots_total: req.storage_slots_total.or(existing.storage_slots_total),
                storage_slots_available: req.storage_slots_available.or(existing.storage_slots_available),
                storage_max_capacity: req.storage_max_capacity.or(existing.storage_max_capacity),
                gpu_type: req.gpu_type.map(|t| match t {
                    crate::modules::laptop::models::GpuType::Integrated => "Integrated".to_string(),
                    crate::modules::laptop::models::GpuType::Dedicated => "Dedicated".to_string(),
                }).unwrap_or(existing.gpu_type),
                gpu_model: req.gpu_model.or(existing.gpu_model),
                screen_size: req.screen_size.unwrap_or(existing.screen_size),
                screen_resolution: match req.screen_resolution {
                    Some(r) => match r {
                        crate::modules::laptop::models::ScreenResolution::FHD => "FHD".to_string(),
                        crate::modules::laptop::models::ScreenResolution::TwoK => "2K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::TwoPointFiveK => "2.5K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::ThreeK => "3K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::ThreePointFiveK => "3.5K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::FourK => "4K".to_string(),
                    },
                    None => match existing.screen_resolution {
                        crate::modules::laptop::models::ScreenResolution::FHD => "FHD".to_string(),
                        crate::modules::laptop::models::ScreenResolution::TwoK => "2K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::TwoPointFiveK => "2.5K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::ThreeK => "3K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::ThreePointFiveK => "3.5K".to_string(),
                        crate::modules::laptop::models::ScreenResolution::FourK => "4K".to_string(),
                    },
                },
                refresh_rate: req.refresh_rate.unwrap_or(existing.refresh_rate),
                weight: req.weight.or(existing.weight),
                operating_system: req.operating_system.or(existing.operating_system),
            };

            self.repository.update(id, updated_spec).await
        })
        .await
    }

    async fn delete_specification(&self, id: &Uuid) -> Result<()> {
        // Check if specification exists
        self.get_specification_by_id(id).await?;

        self.repository.delete(id).await
    }

    async fn delete_specification_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        self.repository.delete_by_laptop_id(laptop_id).await
    }
}

// ===== PRICE SERVICE =====

#[derive(Clone)]
pub struct PriceService {
    repository: DynPriceRepo,
    laptop_service: Arc<dyn LaptopServiceTrait>,
}

impl PriceService {
    pub fn new(
        database: Database,
        laptop_service: Arc<dyn LaptopServiceTrait>,
    ) -> Self {
        Self {
            repository: Arc::new(DieselPriceRepository::new(database)),
            laptop_service,
        }
    }
}

#[async_trait]
impl PriceServiceTrait for PriceService {
    async fn create_price(&self, request: CreatePriceRequest, created_by: &Uuid) -> Result<Price> {
        validate_enhanced_and_execute(request, |req| async move {
            // Validate laptop exists
            self.laptop_service.get_laptop_by_id(&req.laptop_id).await?;

            // Validate price range
            if req.max_price < req.min_price {
                return Err(AppError::Validation(
                    "Max price must be greater than or equal to min price".to_string()
                ));
            }

            let new_price = NewPrice {
                id: Uuid::new_v4(),
                laptop_id: req.laptop_id,
                min_price: req.min_price,
                max_price: req.max_price,
                currency: req.currency.unwrap_or_else(|| "USD".to_string()),
                source: req.source,
                region: req.region.unwrap_or_else(|| "Global".to_string()),
                effective_date: req.effective_date.unwrap_or_else(|| Utc::now().date_naive()),
                is_current: req.is_current.unwrap_or(true),
                created_by: *created_by,
            };

            self.repository.create(new_price).await
        })
        .await
    }

    async fn get_price_by_id(&self, id: &Uuid) -> Result<Price> {
        self.repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("Price", Some(&id.to_string())))
    }

    async fn get_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.repository.find_by_laptop_id(laptop_id).await
    }

    async fn get_current_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.repository.find_current_by_laptop_id(laptop_id).await
    }

    async fn update_price(&self, id: &Uuid, request: UpdatePriceRequest, _updated_by: &Uuid) -> Result<Price> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if price exists
            let existing = self.get_price_by_id(id).await?;

            // Validate price range if both are provided
            let min_price = req.min_price.unwrap_or(existing.min_price);
            let max_price = req.max_price.unwrap_or(existing.max_price);

            if max_price < min_price {
                return Err(AppError::Validation(
                    "Max price must be greater than or equal to min price".to_string()
                ));
            }

            let updated_price = NewPrice {
                id: existing.id,
                laptop_id: existing.laptop_id,
                min_price,
                max_price,
                currency: req.currency.unwrap_or(existing.currency),
                source: req.source.or(existing.source),
                region: req.region.unwrap_or(existing.region),
                effective_date: req.effective_date.unwrap_or(existing.effective_date),
                is_current: req.is_current.unwrap_or(existing.is_current),
                created_by: existing.created_by,
            };

            self.repository.update(id, updated_price).await
        })
        .await
    }

    async fn delete_price(&self, id: &Uuid) -> Result<()> {
        // Check if price exists
        self.get_price_by_id(id).await?;

        self.repository.delete(id).await
    }

    async fn delete_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        self.repository.delete_by_laptop_id(laptop_id).await
    }

    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()> {
        // Validate laptop exists
        self.laptop_service.get_laptop_by_id(laptop_id).await?;

        // Validate price exists and belongs to the laptop
        let price = self.get_price_by_id(price_id).await?;
        if price.laptop_id != *laptop_id {
            return Err(AppError::BadRequest(
                "Price does not belong to the specified laptop".to_string()
            ));
        }

        self.repository.set_current_price(laptop_id, price_id).await
    }
}

// ===== COMBINED LAPTOP MANAGEMENT SERVICE =====

#[derive(Clone)]
pub struct LaptopManagementService {
    laptop_service: Arc<dyn LaptopServiceTrait>,
    specification_service: Arc<dyn SpecificationServiceTrait>,
    price_service: Arc<dyn PriceServiceTrait>,
}

impl LaptopManagementService {
    pub fn new(
        database: Database,
        category_service: Arc<dyn CategoryServiceTrait>,
    ) -> Self {
        let laptop_service = Arc::new(LaptopService::new(database.clone(), category_service));
        let specification_service = Arc::new(SpecificationService::new(database.clone(), laptop_service.clone()));
        let price_service = Arc::new(PriceService::new(database, laptop_service.clone()));

        Self {
            laptop_service,
            specification_service,
            price_service,
        }
    }
}

#[async_trait]
impl LaptopManagementServiceTrait for LaptopManagementService {
    // Laptop operations - delegate to laptop service
    async fn create_laptop(&self, request: CreateLaptopRequest, created_by: &Uuid) -> Result<LaptopFullView> {
        self.laptop_service.create_laptop(request, created_by).await
    }

    async fn get_laptop_by_id(&self, id: &Uuid) -> Result<LaptopFullView> {
        self.laptop_service.get_laptop_by_id(id).await
    }

    async fn get_laptop_by_slug(&self, slug: &str) -> Result<LaptopFullView> {
        self.laptop_service.get_laptop_by_slug(slug).await
    }

    async fn update_laptop(&self, id: &Uuid, request: UpdateLaptopRequest, updated_by: &Uuid) -> Result<LaptopFullView> {
        self.laptop_service.update_laptop(id, request, updated_by).await
    }

    async fn delete_laptop(&self, id: &Uuid) -> Result<()> {
        // Delete related data first
        self.specification_service.delete_specification_by_laptop_id(id).await?;
        self.price_service.delete_prices_by_laptop_id(id).await?;

        // Then delete the laptop
        self.laptop_service.delete_laptop(id).await
    }

    // Public API operations
    async fn get_public_laptops(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsPublic> {
        self.laptop_service.get_public_laptops(request).await
    }

    async fn get_public_laptop_by_slug(&self, slug: &str) -> Result<LaptopPublicView> {
        self.laptop_service.get_public_laptop_by_slug(slug).await
    }

    async fn increment_laptop_view_count(&self, id: &Uuid) -> Result<()> {
        self.laptop_service.increment_laptop_view_count(id).await
    }

    // Private API operations
    async fn get_detailed_laptops(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsDetailed> {
        self.laptop_service.get_detailed_laptops(request).await
    }

    async fn get_full_laptops(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsFull> {
        self.laptop_service.get_full_laptops(request).await
    }

    // Status management
    async fn publish_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        self.laptop_service.publish_laptop(id, updated_by).await
    }

    async fn archive_laptop(&self, id: &Uuid, updated_by: &Uuid) -> Result<()> {
        self.laptop_service.archive_laptop(id, updated_by).await
    }

    async fn set_laptop_featured(&self, id: &Uuid, is_featured: bool, updated_by: &Uuid) -> Result<()> {
        self.laptop_service.set_laptop_featured(id, is_featured, updated_by).await
    }

    // Specification operations - delegate to specification service
    async fn create_specification(&self, request: CreateSpecificationRequest) -> Result<Specification> {
        self.specification_service.create_specification(request).await
    }

    async fn get_specification_by_id(&self, id: &Uuid) -> Result<Specification> {
        self.specification_service.get_specification_by_id(id).await
    }

    async fn get_specification_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Option<Specification>> {
        self.specification_service.get_specification_by_laptop_id(laptop_id).await
    }

    async fn update_specification(&self, id: &Uuid, request: UpdateSpecificationRequest) -> Result<Specification> {
        self.specification_service.update_specification(id, request).await
    }

    async fn delete_specification(&self, id: &Uuid) -> Result<()> {
        self.specification_service.delete_specification(id).await
    }

    // Price operations - delegate to price service
    async fn create_price(&self, request: CreatePriceRequest, created_by: &Uuid) -> Result<Price> {
        self.price_service.create_price(request, created_by).await
    }

    async fn get_price_by_id(&self, id: &Uuid) -> Result<Price> {
        self.price_service.get_price_by_id(id).await
    }

    async fn get_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.price_service.get_prices_by_laptop_id(laptop_id).await
    }

    async fn get_current_prices_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        self.price_service.get_current_prices_by_laptop_id(laptop_id).await
    }

    async fn update_price(&self, id: &Uuid, request: UpdatePriceRequest, updated_by: &Uuid) -> Result<Price> {
        self.price_service.update_price(id, request, updated_by).await
    }

    async fn delete_price(&self, id: &Uuid) -> Result<()> {
        self.price_service.delete_price(id).await
    }

    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()> {
        self.price_service.set_current_price(laptop_id, price_id).await
    }

    // Validation operations
    async fn validate_laptop_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        self.laptop_service.validate_laptop_slug(slug, exclude_id).await
    }

    async fn validate_laptop_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<()> {
        self.laptop_service.validate_laptop_sku(sku, exclude_id).await
    }
}
